"""
Flask application entry point for the BFSI CF Dispute Creation service.
This file serves as the WSGI application entry point referenced in the Dockerfile.
"""

import os
from flask import Flask
from main import index

# Create Flask application
app = Flask(__name__)

# Configure the application
app.config['DEBUG'] = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

# Register the cloud function as a Flask route
@app.route('/', methods=['POST'])
def cloud_function_handler():
    """
    Handle incoming requests and delegate to the cloud function.
    This route mimics the Cloud Functions runtime behavior.
    """
    from flask import request
    import json
    import base64
    
    try:
        # Create a mock cloud event from the Flask request
        # This is a simplified version for local testing
        request_json = request.get_json(silent=True)
        
        if request_json and 'message' in request_json:
            # Handle Pub/Sub message format
            message_data = request_json['message'].get('data', '')
            
            # Create a mock cloud event object
            class MockCloudEvent:
                def __init__(self, data):
                    self.data = data
            
            mock_event = MockCloudEvent({
                'message': {
                    'data': message_data
                }
            })
            
            # Call the main function
            result = index(mock_event)
            return {'status': 'success', 'result': result}, 200
        else:
            return {'error': 'Invalid request format'}, 400
            
    except Exception as e:
        return {'error': str(e)}, 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return {'status': 'healthy'}, 200

if __name__ == '__main__':
    # Run the Flask app in development mode
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=app.config['DEBUG'])
