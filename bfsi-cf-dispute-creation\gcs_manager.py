

import datetime
from typing import Union, Any
from google.cloud import storage
from google.api_core.exceptions import NotFound
from google.oauth2 import service_account
from pytz import timezone

class GoogleCloudStorageManager:
    def __init__(self, credentials: service_account.Credentials, bucket_name: str):
        """
        Initialize the storage client with specific credentials and bucket.
        Args:
            credentials: GCP service account credentials
            bucket_name: Name of the GCS bucket
        """
        if credentials:
            self._storage_client = storage.Client(credentials=credentials)
        else:
            # Use default credentials if no specific credentials provided
            self._storage_client = storage.Client()
        self._bucket_name = bucket_name

    def upload_file(self, path: str, data: Union[bytes, Any]):
        """
        Upload a file to the specified bucket.
        Args:
            path: Destination path in the bucket
            data: File-like object or byte content to upload
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blob = bucket.blob(path)
            blob.upload_from_file(data) if hasattr(
                data, 'read') else blob.upload_from_string(data)
        except Exception as e:
            raise IOError(f"Failed to upload file to {path}: {str(e)}")

    def delete_file(self, path: str):
        """
        Delete a file from the bucket.
        Args:
            path: Path of the file to delete
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blob = bucket.blob(path)
            blob.delete()
        except NotFound:
            print(f"File {path} not found in bucket.")
        except Exception as e:
            raise IOError(f"Failed to delete file {path}: {str(e)}")

    def read_file(self, path: str) -> bytes:
        """
        Read a file from the bucket.
        Args:
            path: Path of the file to read
        Returns:
            File content as bytes
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blob = bucket.blob(path)
            return blob.download_as_string()
        except Exception as e:
            raise IOError(f"Failed to read file {path}: {str(e)}")

    def generate_signed_url(self, path: str, expiration_seconds: int = 3600) -> str:
        """
        Generate a signed URL for a file.
        Args:
            path: Path of the file
            expiration_seconds: URL validity duration
        Returns:
            Signed URL string
        """
        try:
            bucket = self._storage_client.bucket(self._bucket_name)
            blob = bucket.blob(path)

            # Use timezone-aware datetime with UTC
            now = datetime.datetime.now(timezone('UTC'))
            expiration_time = now + \
                datetime.timedelta(seconds=expiration_seconds)

            return blob.generate_signed_url(expiration=int(expiration_time.timestamp()))
        except Exception as e:
            raise ValueError(
                f"Failed to generate signed URL for {path}: {str(e)}")
