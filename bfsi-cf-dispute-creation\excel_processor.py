
import pandas as pd
import io
from io import BytesIO

class ExcelProcessor:
    def process_excel(self, excel_content: bytes) -> pd.DataFrame:
        """
        Process Excel file content into a pandas DataFrame.
        Optimized for large files.
        
        Args:
            excel_content: Raw Excel file content
        Returns:
            DataFrame containing the processed Excel data
        """
        try:
            # Create file-like object from bytes
            excel_file = BytesIO(excel_content)
            
            # Read Excel file - don't restrict columns to allow co-borrower fields
            df = pd.read_excel(excel_file)
            
            # Define required columns
            required_columns = [
                "Borrower's Number",
                "Name of the Borrower",
                "Loan ID",
            ]
            
            # Check if required columns exist
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Excel file is missing required columns: {', '.join(missing_columns)}")
            
            # Remove any duplicate rows based on Loan ID
            if 'Loan ID' in df.columns:
                df = df.drop_duplicates(subset=['Loan ID'])
            
            # Clean data - convert to strings and strip whitespace for core fields
            for col in ['<PERSON>rrow<PERSON>\'s Number', 'Name of the Borrower', 'Loan ID']:
                if col in df.columns:
                    df[col] = df[col].astype(str).str.strip()
            
            # Also clean Borrower's Email if it exists
            if 'Borrower\'s Email' in df.columns:
                df['Borrower\'s Email'] = df['Borrower\'s Email'].astype(str).str.strip()
            return df
            
        except Exception as e:
            raise ValueError(f"Error processing Excel file in ExcelProcessor: {str(e)}")

    def extract_common_data(self, df: pd.DataFrame) -> dict:
        """
        Extract common data from the first row of the Excel sheet.
        Args:
            df: DataFrame containing the Excel data
        Returns:
            Dictionary with common data fields
        """
        common_fields = {
            'address': 'Borrower\'s Address',
            'pincode': 'Pincode',
            'company_name': 'Company Name',
            'case_manager_name': 'Case Manager\'s Name',
            'case_manager_email': 'Case Manager\'s Email',
            'case_manager_phone': 'Case Manager\'s Phone No.'
        }
        
        result = {}
        
        # Check if the DataFrame is empty
        if df.empty:
            return result
            
        # Get all column names from the DataFrame
        columns = df.columns.tolist()
        
        # Extract data only for fields that exist in the DataFrame
        for key, column in common_fields.items():
            if column in columns:
                result[key] = df.iloc[0].get(column)
            else:
                result[key] = None
                
        return result
