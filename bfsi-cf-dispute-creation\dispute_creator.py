
import time
import json
from datetime import datetime
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
import pandas as pd
import re
from pytz import timezone

class DisputeCreator:
    """
    Creates disputes and cases by making database operations.
    Optimized for multi-threaded processing with co-borrower support.
    """
    def __init__(self, pool):
        self.pool = pool
        
        # Cache for profiles to avoid repeated database lookups
        self.profile_cache = {}

    def _extract_co_borrowers(self, row) -> list:
        """
        Extract co-borrower data from a row in the Excel sheet.
        Args:
            row: DataFrame row containing the co-borrower data
        Returns:
            List of co-borrower dictionaries
        """
        co_borrowers = []

        # Check for co-borrower columns using regex pattern
        co_borrower_columns = {}
        for column in row.index:
            # Convert column name to string first to avoid regex errors
            column_str = str(column) if column is not None else ""
            
            # Look for columns with pattern "Name of the Co-borrower<N>"
            name_match = re.match(r'Name of the Co-borrower(\d+)', column_str)
            if name_match:
                index = name_match.group(1)
                if index not in co_borrower_columns:
                    co_borrower_columns[index] = {}
                co_borrower_columns[index]['name'] = column

            # Look for columns with pattern "Co-borrower's Email<N>"
            email_match = re.match(r'Co-borrower\'s Email(\d+)', column_str)
            if email_match:
                index = email_match.group(1)
                if index not in co_borrower_columns:
                    co_borrower_columns[index] = {}
                co_borrower_columns[index]['email'] = column

            # Look for columns with pattern "Co-borrower's Number<N>"
            number_match = re.match(r'Co-borrower\'s Number(\d+)', column_str)
            if number_match:
                index = number_match.group(1)
                if index not in co_borrower_columns:
                    co_borrower_columns[index] = {}
                co_borrower_columns[index]['number'] = column

        # Extract co-borrower data
        for index, columns in co_borrower_columns.items():
            # Safely extract name value
            name_value = row.get(columns.get('name', ''), '')
            name_value = str(name_value) if name_value is not None else ""
            name_value = name_value.strip()

            # Only add co-borrower if name exists and is not empty
            if name_value and name_value not in ['nan', 'None', '']:
                # Safely extract email and number values
                email_value = row.get(columns.get('email', ''), '')
                email_value = str(email_value) if email_value is not None else ""
                email_value = email_value.strip()
                
                number_value = row.get(columns.get('number', ''), '')
                number_value = str(number_value) if number_value is not None else ""
                number_value = number_value.strip()

                # Clean up 'nan' values
                if email_value in ['nan', 'None']:
                    email_value = ''
                if number_value in ['nan', 'None']:
                    number_value = ''

                co_borrower = {
                    'name': name_value,
                    'email': email_value,
                    'phone_number': number_value
                }
                co_borrowers.append(co_borrower)

        return co_borrowers

    def _get_profile_with_user_info(self, email):
        """Get profile with user information in a single query."""
        # Check if profile is already cached
        if email in self.profile_cache:
            return self.profile_cache[email]
            
        query = text("""
            SELECT p.*, u.first_name, u.last_name
            FROM odr_profile p
            JOIN authorization_user u ON p.user_id = u.id
            WHERE u.email = :email
            LIMIT 1
        """)

        try:
            with self.pool.connect() as connection:
                result = connection.execute(query, {"email": email})
                row = result.fetchone()
                if row:
                    profile = {key: value for key, value in zip(result.keys(), row)}
                    # Cache the profile for future use
                    self.profile_cache[email] = profile
                    return profile
                else:
                    return None
        except Exception as e:
            print(f"Database query failed to get profile: {str(e)}")
            raise

    def get_campaign_data(self, campaign_id):
        """Get existing campaign data including current counts and errors."""
        query = text("""
            SELECT number_of_cases_created, processed_rows, processing_errors, 
                   case_creation_status, total_rows
            FROM notice_campaign
            WHERE id = :campaign_id
            LIMIT 1
        """)

        try:
            with self.pool.connect() as connection:
                result = connection.execute(query, {"campaign_id": campaign_id})
                row = result.fetchone()
                if row:
                    return {key: value for key, value in zip(result.keys(), row)}
                else:
                    return None
        except Exception as e:
            print(f"Database query failed to get campaign data: {str(e)}")
            raise

    def _create_dispute(self, connection, dispute):
        """Create a single dispute and return its ID with validation."""
        dispute_query = text("""
            INSERT INTO odr_dispute (name, flow_type, order_date, description, loan_id, created_by_id, created, modified, status, campaign_id, client_id, respondents_email, respondents_name, co_borrowers,claimant_id)
            VALUES (:formatted_name, :flow_type, :order_date, :description, :loan_id, :created_by_id, :created, :modified, 'new', :campaign_id, :client_id, :respondents_email, :respondents_name, :co_borrowers, :claimant_id)
            RETURNING id
        """)

        try:
            result = connection.execute(dispute_query, {
                "formatted_name": dispute["formatted_name"],
                "flow_type": dispute["flow_type"],
                "order_date": dispute["order_date"],
                "description": dispute["description"],
                "loan_id": dispute["loan_id"],
                "created_by_id": dispute["created_by_id"],
                "created": dispute["created"],
                "modified": dispute["modified"],
                "campaign_id": dispute["campaign_id"],
                "client_id": dispute["client_id"],
                "respondents_email": dispute["respondents_email"],
                "respondents_name": dispute["respondents_name"],
                "co_borrowers": dispute["co_borrowers"],
                "claimant_id": dispute["claimant_id"]
            })
            
            dispute_id = result.fetchone()[0]
            if not dispute_id:
                raise ValueError("Failed to get dispute ID after insertion")
            
            return dispute_id
            
        except Exception as e:
            print(f"Error creating dispute for loan {dispute.get('loan_id', 'unknown')}: {str(e)}")
            raise

    def update_campaign_status(self, campaign_id, processed_rows, cases_created, errors, status='processing', total_rows=None):
        """Update campaign status with error information."""
        try:
            errors_json = json.dumps(errors)
            updated_at = datetime.now(timezone('UTC'))
            
            # Build the update query dynamically based on provided parameters
            update_fields = [
                "number_of_cases_created = :cases_created",
                "processing_errors = :errors",
                "processed_rows = :processed_rows",
                "case_creation_status = :status",
                "updated_at = :updated_at"
            ]
            
            params = {
                "cases_created": cases_created,
                "errors": errors_json,
                "processed_rows": processed_rows,
                "status": status,
                "updated_at": updated_at,
                "campaign_id": campaign_id
            }
            
            # Only update total_rows if explicitly provided
            if total_rows is not None:
                update_fields.append("total_rows = :total_rows")
                params["total_rows"] = total_rows
            
            update_query = text(f"""
                UPDATE notice_campaign
                SET {', '.join(update_fields)}
                WHERE id = :campaign_id
            """)

            with self.pool.connect() as connection:
                connection.execute(update_query, params)
                connection.commit()
        except SQLAlchemyError as e:
            print(f"Error updating campaign status: {str(e)}")

    def attach_status_to_excel(self, df, created_users, errors):
        """Attach processing status to Excel report."""
        df_report = df.copy()
        df_report['status'] = 'failed'
        df_report['failed_reason'] = ''

        # Initialize error_map
        error_map = {}
        if errors:
            for e in errors:
                if 'row' in e:
                    error_map[e['row'] - 2] = e['error_detail']
        
        # Create a set of successful loan/phone pairs for fast lookup
        success_set = set()
        if created_users:
            for u in created_users:
                if 'loan_number' in u and 'phone_number' in u:
                    success_set.add((u['loan_number'], u['phone_number']))

        # Update status for each row
        for idx, row in df_report.iterrows():
            loan = str(row.get("Loan ID", "")).strip()
            phone = str(row.get("Borrower's Number", "")).strip()

            if idx in error_map:
                df_report.at[idx, 'failed_reason'] = error_map[idx]
            elif (loan, phone) in success_set:
                df_report.at[idx, 'status'] = 'success'
            else:
                df_report.at[idx, 'failed_reason'] = 'Unknown error'
                
        return df_report

    def attach_status_to_excel_incremental(self, df, created_users, errors, existing_loan_ids):
        """
        Attach processing status to Excel report for incremental processing.
        Handles both new and existing disputes appropriately.
        """
        df_report = df.copy()
        df_report['status'] = 'failed'
        df_report['failed_reason'] = ''

        # Initialize error_map for new errors only
        error_map = {}
        if errors:
            for e in errors:
                if 'row' in e:
                    error_map[e['row'] - 2] = e['error_detail']
        
        # Create a set of successful loan/phone pairs for newly created disputes
        success_set = set()
        if created_users:
            for u in created_users:
                if 'loan_number' in u and 'phone_number' in u:
                    success_set.add((u['loan_number'], u['phone_number']))

        # Update status for each row
        for idx, row in df_report.iterrows():
            loan = str(row.get("Loan ID", "")).strip()
            phone = str(row.get("Borrower's Number", "")).strip()

            # Check if this loan already existed before this processing
            if loan in existing_loan_ids:
                df_report.at[idx, 'status'] = 'already_exists'
                df_report.at[idx, 'failed_reason'] = 'Dispute already created for this loan ID'
            elif idx in error_map:
                df_report.at[idx, 'failed_reason'] = error_map[idx]
            elif (loan, phone) in success_set:
                df_report.at[idx, 'status'] = 'success'
                df_report.at[idx, 'failed_reason'] = 'Newly created in this processing'
            else:
                df_report.at[idx, 'failed_reason'] = 'Unknown error'
                
        return df_report

    def _get_loanids_for_campaign(self, campaign_id):
        """Get loan_ids for campaign."""                    
        query = text("""
            SELECT distinct loan_id from odr_dispute WHERE campaign_id = :campaign_id
        """)

        try:
            with self.pool.connect() as connection:
                results = connection.execute(query, {"campaign_id": campaign_id})
                if results:
                    loan_ids = [result[0] for result in results]
                    return loan_ids
                else:
                    return []
        except Exception as e:
            print(f"Database query failed to get loan IDs for campaign: {str(e)}")
            raise
