

import os
import base64
import json
from google.oauth2 import service_account
import functions_framework
import sqlalchemy
from google.cloud.sql.connector import Connector
from io import BytesIO
import pandas as pd
import concurrent.futures
import time
import threading
from datetime import datetime
from pytz import timezone

# Import custom modules
from gcs_manager import GoogleCloudStorageManager
from excel_processor import ExcelProcessor
from dispute_creator import DisputeCreator

# Initialize GCS manager
GCS_BUCKET_NAME = os.environ.get('GCS_BUCKET_NAME')
credentials_json = os.getenv('GCP_CREDENTIALS').strip()
credentials_dict = json.loads(credentials_json)
STORAGE_CREDENTIALS = service_account.Credentials.from_service_account_info(credentials_dict)

gcs_manager = GoogleCloudStorageManager(credentials=STORAGE_CREDENTIALS, bucket_name=GCS_BUCKET_NAME)

# Configuration for multi-threading - Reduced for better stability
MAX_THREADS = 4  # Reduced to prevent connection pool exhaustion
BATCH_SIZE = 100  # Smaller batches for better transaction management

# Global connection pool
pool = None

def connect_to_instance() -> sqlalchemy.engine.base.Engine:    
    connector = Connector(refresh_strategy="lazy")
    instance_conn_name = os.environ['INSTANCE_CONNECTION_NAME']
    db_name = os.environ.get('DB_NAME')
    db_user = os.environ.get('DB_USER')
    db_password = os.environ.get('DB_PASSWORD')
    
    def getconn():
        conn = connector.connect(
            instance_conn_name,
            'pg8000',
            user=db_user,
            password=db_password,
            db=db_name
        )
        return conn

    # Optimize connection pool for multithreading with better stability
    return sqlalchemy.create_engine(
        "postgresql+pg8000://",
        creator=getconn,
        pool_size=MAX_THREADS * 3,  # Increased pool size
        max_overflow=MAX_THREADS * 4,  # Increased overflow
        pool_timeout=120,  # Increased timeout
        pool_recycle=300,
        pool_pre_ping=True,  # Enable connection validation
        echo=False  # Disable SQL logging for performance
    )

@functions_framework.cloud_event
def index(cloud_event):
    try:
        # Decode message data
        pubsub_message = base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        data = json.loads(pubsub_message)

        # Process the message
        result = process_message(data)
        
        # Return success
        return result

    except Exception as e:
        print(f"Error processing message: {str(e)}")
        return f"Error: {str(e)}"
    finally:
        # Clean up global pool if it exists
        global pool
        if pool:
            pool.dispose()
            print("Database connection pool closed")

def process_message(data):
    """
    Process the message data to create disputes and cases.
    Args:
        data: Dictionary containing the message data
    """
    df_report = None
    file_name = None
    dispute_creator = None
    
    try:
        # Extract parameters from the message
        campaign_id = data.get('campaign_id')
        file_path = data.get('file_path')
        flow_type = data.get('flow_type')
        claimant_email = data.get('claimant_email')
        client_email = data.get('client_email')
        user_id = data.get('user_id')
        profile_id = data.get('profile_id')
        has_co_borrowers = data.get('has_co_borrowers', False)

        # Convert has_co_borrowers to a boolean if it's a string
        if isinstance(has_co_borrowers, str):
            has_co_borrowers = has_co_borrowers.lower() == 'true'
        print(f"Co-borrowers processing is {'enabled' if has_co_borrowers else 'disabled'}")

        file_name = file_path.split('/')[-1]

        # Validate required parameters
        if not all([campaign_id, file_path, flow_type, claimant_email, client_email, user_id, profile_id]):
            # print(f"Missing params: campaign_id={campaign_id}, file_path={file_path}, flow_type={flow_type}, "
            #       f"claimant_email={claimant_email}, client_email={client_email}, user_id={user_id}, profile_id={profile_id}")
            raise ValueError("Missing required parameters")

        print(f"Processing campaign {campaign_id}, file: {file_path}")

        # Initialize database connection pool
        global pool
        if not pool:
            pool = connect_to_instance()

        # Initialize DisputeCreator with the connection pool
        dispute_creator = DisputeCreator(pool)
        
        # Get existing campaign data to determine if this is incremental processing
        existing_campaign_data = dispute_creator.get_campaign_data(campaign_id)
        if not existing_campaign_data:
            raise ValueError(f"Campaign {campaign_id} not found")
        
        # Get existing disputes for this campaign
        existing_loan_ids = set(dispute_creator._get_loanids_for_campaign(campaign_id) or [])
        existing_disputes_count = len(existing_loan_ids)
        existing_processed_rows = existing_campaign_data.get('processed_rows', 0)
        existing_errors = existing_campaign_data.get('processing_errors', [])
        
        # Parse existing errors if they're stored as JSON string
        if isinstance(existing_errors, str):
            try:
                existing_errors = json.loads(existing_errors) if existing_errors else []
            except json.JSONDecodeError:
                existing_errors = []
        
        print(f"Existing campaign data - Disputes: {existing_disputes_count}, Processed rows: {existing_processed_rows}, Errors: {len(existing_errors)}")
        
        # Update campaign status to processing
        dispute_creator.update_campaign_status(
            campaign_id=campaign_id,
            cases_created=existing_disputes_count,
            processed_rows=existing_processed_rows,
            status='processing',
            errors=existing_errors,
            total_rows=existing_campaign_data.get('total_rows', 0)
        )

        # Download Excel file from GCS
        excel_content = gcs_manager.read_file(file_path)

        # Process Excel file
        processor = ExcelProcessor()
        df = processor.process_excel(excel_content)
        
        # Extract common data
        common_data = processor.extract_common_data(df)
        
        # Get profiles once before threading for reuse
        claimant_profile = dispute_creator._get_profile_with_user_info(claimant_email)
        if not claimant_profile:
            raise ValueError(f"Claimant profile not found for email: {claimant_email}")
            
        client_profile = dispute_creator._get_profile_with_user_info(client_email)
        if not client_profile:
            raise ValueError(f"Client profile not found for email: {client_email}")

        # Filter out rows that already have disputes created
        df_new = df[~df['Loan ID'].astype(str).str.strip().isin(existing_loan_ids)].copy()
        df_existing = df[df['Loan ID'].astype(str).str.strip().isin(existing_loan_ids)].copy()
        
        total_rows_in_file = len(df)
        new_rows_to_process = len(df_new)
        existing_rows_skipped = len(df_existing)
        
        print(f"File analysis - Total rows: {total_rows_in_file}, New rows to process: {new_rows_to_process}, Existing rows (skipped): {existing_rows_skipped}")
        
        # Update campaign total_rows if this is the first time or if file has more rows
        updated_total_rows = max(existing_campaign_data.get('total_rows', 0), total_rows_in_file)
        
        # If no new rows to process, return early
        if new_rows_to_process == 0:
            print("No new rows to process - all disputes already exist")
            
            # Create report with all existing rows marked as already processed
            df_report = df.copy()
            df_report['status'] = 'already_exists'
            df_report['failed_reason'] = 'Dispute already created for this loan ID'
            
            # Update campaign status to completed
            dispute_creator.update_campaign_status(
                campaign_id=campaign_id,
                cases_created=existing_disputes_count,
                processed_rows=total_rows_in_file,  # All rows are now "processed"
                status='completed',
                errors=existing_errors,
                total_rows=updated_total_rows
            )
            
            return f"Function completed: No new cases to create. {existing_disputes_count} cases already exist for {total_rows_in_file} rows"
        
        # Create a thread-safe list to store results from all threads
        created_users = []
        errors = list(existing_errors)  # Start with existing errors
        processed_rows_counter = [existing_processed_rows]  # Start with existing processed rows
        results_lock = threading.Lock()  # Lock for thread-safe updates to shared resources

        # Function to process a single batch with better error handling
        # def process_batch(batch_df, batch_index):
        #     batch_created_users = []
        #     batch_errors = []
            
        #     batch_start_time = time.time()
        #     print(f"Starting batch {batch_index} with {len(batch_df)} rows")
            
        #     try:
        #         batch_disputes = []
                
        #         # Prepare data for batch processing
        #         for index, row in batch_df.iterrows():
        #             try:
        #                 # Extract row data
        #                 phone_number = str(row.get('Borrower\'s Number', '')).strip()
        #                 customer_name = str(row.get('Name of the Borrower', '')).strip()
        #                 loan_number = str(row.get('Loan ID', '')).strip()
        #                 respondents_email = str(row.get('Borrower\'s Email', '')).strip()

        #                 # Validate required fields
        #                 missing_fields = []
        #                 # if not phone_number or phone_number == 'nan': missing_fields.append("Borrower's Number")
        #                 # if not customer_name or customer_name == 'nan': missing_fields.append("Name of the Borrower")
        #                 if not loan_number or loan_number == 'nan': missing_fields.append("Loan ID")

        #                 if missing_fields:
        #                     batch_errors.append({
        #                         'row': index + 2,
        #                         'error_type': 'missing_fields',
        #                         'error_detail': f'Missing fields: {", ".join(missing_fields)}',
        #                         'loan_number': loan_number
        #                     })
        #                     continue

        #                 # Double-check that this loan doesn't already exist (safety check)
        #                 if loan_number in existing_loan_ids:
        #                     continue

        #                 # Extract co-borrowers data if requested
        #                 co_borrowers = []
        #                 if has_co_borrowers:
        #                     co_borrowers = dispute_creator._extract_co_borrowers(row)
        #                     # print(f"Found {len(co_borrowers)} co-borrowers for loan {loan_number}")

        #                 # Prepare dispute data
        #                 claimant_name = f"{claimant_profile['first_name']} {claimant_profile['last_name']}"
        #                 formatted_name = f"{claimant_name} vs {customer_name}"
                        
        #                 current_time = datetime.now(timezone('UTC'))
                        
        #                 # Convert co_borrowers list to JSON string
        #                 co_borrowers_json = json.dumps(co_borrowers)
                        
        #                 # Add to batch data
        #                 batch_disputes.append({
        #                     "formatted_name": formatted_name,
        #                     "flow_type": flow_type,
        #                     "order_date": current_time.date(),
        #                     "description": formatted_name,
        #                     "loan_id": loan_number,
        #                     "created_by_id": profile_id,
        #                     "created": current_time,
        #                     "modified": current_time,
        #                     "campaign_id": campaign_id,
        #                     "client_id": client_profile['id'],
        #                     "respondents_email": respondents_email,
        #                     "respondents_name": customer_name,
        #                     "co_borrowers": co_borrowers_json,
        #                     "claimant_id": claimant_profile['id']
        #                 })
        #             except Exception as e:
        #                 print(f"Error preparing row {index}: {str(e)}")
        #                 batch_errors.append({
        #                     'row': index + 2,
        #                     'loan_number': loan_number if 'loan_number' in locals() else 'Unknown',
        #                     'error_type': 'processing_error',
        #                     'error_detail': str(e)
        #                 })
                
        #         # Process batch disputes with retry mechanism
        #         if batch_disputes:
        #             max_retries = 3
        #             retry_count = 0
                    
        #             while retry_count < max_retries:
        #                 try:
        #                     # Use a fresh connection for each batch to avoid connection issues
        #                     with pool.begin() as connection:
        #                         # Process each dispute individually with error tracking
        #                         for dispute in batch_disputes:
        #                             try:
        #                                 # Create dispute
        #                                 dispute_id = dispute_creator._create_dispute(connection, dispute)
                                        
        #                                 # Add to created users
        #                                 batch_created_users.append({
        #                                     'phone_number': dispute['phone_number'],
        #                                     'loan_number': dispute['loan_id'],
        #                                     'dispute_id': dispute_id
        #                                 })
                                        
        #                             except Exception as e:
        #                                 print(f"Error creating dispute for row {dispute['row_index']} (loan: {dispute['loan_id']}): {str(e)}")
        #                                 batch_errors.append({
        #                                     'row': dispute['row_index'] + 2,
        #                                     'loan_number': dispute['loan_id'],
        #                                     'error_type': 'database_error',
        #                                     'error_detail': str(e)
        #                                 })
                                
        #                         # If we reach here, the transaction was successful
        #                         break
                                
        #                 except Exception as e:
        #                     retry_count += 1
        #                     print(f"Database error in batch {batch_index}, attempt {retry_count}/{max_retries}: {str(e)}")
                            
        #                     if retry_count >= max_retries:
        #                         # Mark all remaining disputes as failed
        #                         for dispute in batch_disputes:
        #                             # Only add error if not already added
        #                             if not any(err.get('row') == dispute['row_index'] + 2 for err in batch_errors):
        #                                 batch_errors.append({
        #                                     'row': dispute['row_index'] + 2,
        #                                     'loan_number': dispute['loan_id'],
        #                                     'error_type': 'database_error',
        #                                     'error_detail': f'Failed after {max_retries} retries: {str(e)}'
        #                                 })
        #                     else:
        #                         # Wait before retry
        #                         time.sleep(1)
                
        #         batch_end_time = time.time()
        #         batch_duration = batch_end_time - batch_start_time
        #         print(f"Completed batch {batch_index} in {batch_duration:.2f} seconds. Created {len(batch_created_users)} disputes, {len(batch_errors)} errors.")
                
        #         # Update global results with thread-safe lock
        #         with results_lock:
        #             created_users.extend(batch_created_users)
        #             errors.extend(batch_errors)
        #             processed_rows_counter[0] += len(batch_df)
                    
        #             # Calculate total counts including existing data
        #             total_created = existing_disputes_count + len(created_users)
        #             total_processed = processed_rows_counter[0]
                    
        #             # Report progress more frequently for better tracking
        #             if batch_index % 3 == 0 or batch_index == num_batches - 1:
        #                 dispute_creator.update_campaign_status(
        #                     campaign_id=campaign_id,
        #                     cases_created=total_created,
        #                     processed_rows=total_processed,
        #                     status='processing',
        #                     errors=errors,
        #                     total_rows=updated_total_rows
        #                 )
                
        #     except Exception as e:
        #         print(f"Critical error processing batch {batch_index}: {str(e)}")
        #         with results_lock:
        #             # Add error for all rows in this batch if not already added
        #             for index, row in batch_df.iterrows():
        #                 if not any(err.get('row') == index + 2 for err in errors):
        #                     errors.append({
        #                         'row': index + 2,
        #                         'batch': batch_index,
        #                         'loan_number': str(row.get('Loan ID', '')).strip(),
        #                         'error_type': 'batch_processing_error',
        #                         'error_detail': str(e)
        #                     })
        # Fixed section of main.py - around line 200-350

        def process_batch(batch_df, batch_index):
            batch_created_users = []
            batch_errors = []
            
            batch_start_time = time.time()
            print(f"Starting batch {batch_index} with {len(batch_df)} rows")
            
            try:
                batch_disputes = []
                
                # Prepare data for batch processing
                for index, row in batch_df.iterrows():
                    try:
                        # Extract row data
                        phone_number = str(row.get('Borrower\'s Number', '')).strip()
                        customer_name = str(row.get('Name of the Borrower', '')).strip()
                        loan_number = str(row.get('Loan ID', '')).strip()
                        respondents_email = str(row.get('Borrower\'s Email', '')).strip()

                        # Validate required fields
                        missing_fields = []
                        if not loan_number or loan_number == 'nan': 
                            missing_fields.append("Loan ID")

                        if missing_fields:
                            batch_errors.append({
                                'row': index + 2,
                                'error_type': 'missing_fields',
                                'error_detail': f'Missing fields: {", ".join(missing_fields)}',
                                'loan_number': loan_number
                            })
                            continue

                        # Double-check that this loan doesn't already exist (safety check)
                        if loan_number in existing_loan_ids:
                            continue

                        # Extract co-borrowers data if requested
                        co_borrowers = []
                        if has_co_borrowers:
                            co_borrowers = dispute_creator._extract_co_borrowers(row)

                        # Prepare dispute data
                        claimant_name = f"{claimant_profile['first_name']} {claimant_profile['last_name']}"
                        formatted_name = f"{claimant_name} vs {customer_name}"
                        
                        current_time = datetime.now(timezone('UTC'))
                        
                        # Convert co_borrowers list to JSON string
                        co_borrowers_json = json.dumps(co_borrowers)
                        
                        # Add to batch data
                        batch_disputes.append({
                            "formatted_name": formatted_name,
                            "flow_type": flow_type,
                            "order_date": current_time.date(),
                            "description": formatted_name,
                            "loan_id": loan_number,
                            "created_by_id": profile_id,
                            "created": current_time,
                            "modified": current_time,
                            "campaign_id": campaign_id,
                            "client_id": client_profile['id'],
                            "respondents_email": respondents_email,
                            "respondents_name": customer_name,
                            "co_borrowers": co_borrowers_json,
                            "claimant_id": claimant_profile['id'],
                            "phone_number": phone_number,
                            "row_index": index
                        })
                    except Exception as e:
                        print(f"Error preparing row {index}: {str(e)}")
                        batch_errors.append({
                            'row': index + 2,
                            'loan_number': loan_number if 'loan_number' in locals() else 'Unknown',
                            'error_type': 'processing_error',
                            'error_detail': str(e)
                        })
                
                # Process batch disputes with retry mechanism
                if batch_disputes:
                    max_retries = 3
                    retry_count = 0
                    
                    while retry_count < max_retries:
                        try:
                            # Use a fresh connection for each batch to avoid connection issues
                            with pool.begin() as connection:
                                # Process each dispute individually with error tracking
                                for dispute in batch_disputes:
                                    try:
                                        # Create dispute
                                        dispute_id = dispute_creator._create_dispute(connection, dispute)
                                        
                                        # Add to created users
                                        batch_created_users.append({
                                            'phone_number': dispute['phone_number'],
                                            'loan_number': dispute['loan_id'],
                                            'dispute_id': dispute_id
                                        })
                                        
                                    except Exception as e:
                                        print(f"Error creating dispute for row {dispute['row_index']} (loan: {dispute['loan_id']}): {str(e)}")
                                        batch_errors.append({
                                            'row': dispute['row_index'] + 2,
                                            'loan_number': dispute['loan_id'],
                                            'error_type': 'database_error',
                                            'error_detail': str(e)
                                        })
                                
                                # If we reach here, the transaction was successful
                                break
                                
                        except Exception as e:
                            retry_count += 1
                            print(f"Database error in batch {batch_index}, attempt {retry_count}/{max_retries}: {str(e)}")
                            
                            if retry_count >= max_retries:
                                # Mark all remaining disputes as failed
                                for dispute in batch_disputes:
                                    # Only add error if not already added
                                    if not any(err.get('row') == dispute['row_index'] + 2 for err in batch_errors):
                                        batch_errors.append({
                                            'row': dispute['row_index'] + 2,
                                            'loan_number': dispute['loan_id'],
                                            'error_type': 'database_error',
                                            'error_detail': f'Failed after {max_retries} retries: {str(e)}'
                                        })
                            else:
                                # Wait before retry
                                time.sleep(1)
                
                batch_end_time = time.time()
                batch_duration = batch_end_time - batch_start_time
                print(f"Completed batch {batch_index} in {batch_duration:.2f} seconds. Created {len(batch_created_users)} disputes, {len(batch_errors)} errors.")
                
                # Update global results with thread-safe lock
                with results_lock:
                    created_users.extend(batch_created_users)
                    errors.extend(batch_errors)
                    processed_rows_counter[0] += len(batch_df)
                    
                    # Calculate total counts including existing data
                    total_created = existing_disputes_count + len(created_users)
                    total_processed = processed_rows_counter[0]
                    
                    # Report progress more frequently for better tracking
                    if batch_index % 3 == 0 or batch_index == num_batches - 1:
                        dispute_creator.update_campaign_status(
                            campaign_id=campaign_id,
                            cases_created=total_created,
                            processed_rows=total_processed,
                            status='processing',
                            errors=errors,
                            total_rows=updated_total_rows
                        )
                
            except Exception as e:
                print(f"Critical error processing batch {batch_index}: {str(e)}")
                with results_lock:
                    # Add error for all rows in this batch if not already added
                    for index, row in batch_df.iterrows():
                        if not any(err.get('row') == index + 2 for err in errors):
                            errors.append({
                                'row': index + 2,
                                'batch': batch_index,
                                'loan_number': str(row.get('Loan ID', '')).strip(),
                                'error_type': 'batch_processing_error',
                                'error_detail': str(e)
                            })
        # Split into batches and process in parallel
        batches = []
        for i in range(0, len(df_new), BATCH_SIZE):
            batches.append(df_new.iloc[i:i+BATCH_SIZE])
        
        num_batches = len(batches)
        print(f"Splitting {new_rows_to_process} new rows into {num_batches} batches for parallel processing")
        
        # Use ThreadPoolExecutor for parallel processing with better error handling
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
            # Submit all batches for processing
            future_to_batch = {executor.submit(process_batch, batch, i): i for i, batch in enumerate(batches)}
            
            # Wait for all futures to complete and handle exceptions
            completed_batches = 0
            for future in concurrent.futures.as_completed(future_to_batch):
                batch_index = future_to_batch[future]
                try:
                    future.result()  # This will raise exception if the thread failed
                    completed_batches += 1
                except Exception as e:
                    print(f"Exception in batch {batch_index}: {str(e)}")
                    with results_lock:
                        # Add errors for all rows in the failed batch
                        failed_batch = batches[batch_index]
                        for index, row in failed_batch.iterrows():
                            errors.append({
                                'row': index + 2,
                                'batch': batch_index,
                                'loan_number': str(row.get('Loan ID', '')).strip(),
                                'error_type': 'thread_exception',
                                'error_detail': str(e)
                            })
        
        # Calculate final counts
        new_disputes_created = len(created_users)
        new_errors_count = len(errors) - len(existing_errors)
        total_disputes_created = existing_disputes_count + new_disputes_created
        total_processed_rows = existing_processed_rows + new_rows_to_process
        
        print(f"Processing complete - New disputes: {new_disputes_created}, New errors: {new_errors_count}")
        print(f"Total counts - Disputes: {total_disputes_created}, Processed rows: {total_processed_rows}, Total errors: {len(errors)}")
        
        # Create final report including both new and existing rows
        df_report = dispute_creator.attach_status_to_excel_incremental(
            df=df,
            created_users=created_users,
            errors=errors[len(existing_errors):],  # Only new errors for this processing
            existing_loan_ids=existing_loan_ids
        )
        
        # Update final campaign status
        final_status = 'completed' if len(errors) == len(existing_errors) else 'completed_with_errors'
        dispute_creator.update_campaign_status(
            campaign_id=campaign_id,
            cases_created=total_disputes_created,
            processed_rows=total_processed_rows,
            status=final_status,
            errors=errors,
            total_rows=updated_total_rows
        )

        return f"Function processed successfully: {new_disputes_created} new cases created, {total_disputes_created} total cases in campaign, {total_processed_rows} total rows processed"

    except Exception as e:
        print(f"Error processing message: {str(e)}")
        if campaign_id and dispute_creator:
            # Get existing data to preserve it during error state
            try:
                existing_data = dispute_creator.get_campaign_data(campaign_id)
                existing_cases = existing_data.get('number_of_cases_created', 0) if existing_data else 0
                existing_processed = existing_data.get('processed_rows', 0) if existing_data else 0
                existing_errors = existing_data.get('processing_errors', []) if existing_data else []
                
                if isinstance(existing_errors, str):
                    try:
                        existing_errors = json.loads(existing_errors) if existing_errors else []
                    except json.JSONDecodeError:
                        existing_errors = []
                
                # Add current error to existing errors
                current_error = {'error_type': 'processing_error', 'error_detail': str(e)}
                existing_errors.append(current_error)
                
                dispute_creator.update_campaign_status(
                    campaign_id=campaign_id,
                    cases_created=existing_cases,
                    processed_rows=existing_processed,
                    status='failed',
                    errors=existing_errors,
                    total_rows=existing_data.get('total_rows', 0) if existing_data else 0
                )
            except Exception as update_error:
                print(f"Error updating campaign status during failure: {str(update_error)}")
        raise
    
    finally:
        # Save report to GCS if we have processed data
        if df_report is not None and campaign_id is not None and file_name is not None:
            excel_buffer = BytesIO()
            with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
                df_report.to_excel(writer, sheet_name='sheet1', index=False)

            # Important: move the pointer back to the beginning of the buffer
            excel_buffer.seek(0)
            report_file_path = f'notices/{campaign_id}/case-creation-reports/case_creation_report_{file_name}'
            gcs_manager.upload_file(report_file_path, excel_buffer)
